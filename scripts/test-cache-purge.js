const { purgeCloudflareCache } = require('../utils/purgecloudflare.ts');

async function testCachePurge() {
  console.log('🧪 Testing Cloudflare cache purge...');
  
  const testUrls = [
    '/api/blogs',
    '/api/blogs/getAllBlogs',
    '/api/blogs/getFeaturedBlogs',
    '/blog/test-slug',
    '/api/blogs/getBlogById/test-id'
  ];
  
  try {
    const result = await purgeCloudflareCache(testUrls);
    
    if (result?.success) {
      console.log('✅ Test passed: Cache purge successful');
      console.log('📊 Result:', result);
    } else {
      console.log('❌ Test failed: Cache purge failed');
      console.log('📊 Error:', result?.error);
    }
  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

// Run test if called directly
if (require.main === module) {
  testCachePurge();
}

module.exports = { testCachePurge };
