export const logCacheDebugInfo = (operation: string, doc: any) => {
  console.log(`🔍 Cache Debug - ${operation}:`, {
    timestamp: new Date().toISOString(),
    docId: doc?.id,
    docSlug: doc?.slug,
    docStatus: doc?.status,
    docTitle: doc?.title?.substring(0, 50) + '...'
  });
};

export const validateCacheUrls = (urls: string[]) => {
  const validUrls = [];
  const invalidUrls = [];
  
  urls.forEach(url => {
    if (typeof url === 'string' && url.trim().length > 0) {
      validUrls.push(url);
    } else {
      invalidUrls.push(url);
    }
  });
  
  if (invalidUrls.length > 0) {
    console.warn('⚠️ Invalid URLs found:', invalidUrls);
  }
  
  return validUrls;
};
