

export const purgeCloudflareCache = async (paths: string[]) => {
  const zoneId = process.env.CLOUDFLARE_ZONE_ID;
  const token = process.env.CLOUDFLARE_API_TOKEN;
  const domain = process.env.CLOUDFLARE_SITE_URL;

  if (!zoneId || !token || !domain) {
    console.error('❌ Missing Cloudflare ENV vars:', { zoneId: !!zoneId, token: !!token, domain: !!domain });
    return { success: false, error: 'Missing environment variables' };
  }

  // Clean and validate paths
  const cleanPaths = paths.filter(path => path && typeof path === 'string');
  const urls = cleanPaths.map((path) => {
    // Ensure path starts with /
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `${domain}${cleanPath}`;
  });

  console.log("🔄 Purging Cloudflare cache for URLs:", urls);

  try {
    const res = await fetch(
      `https://api.cloudflare.com/client/v4/zones/${zoneId}/purge_cache`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ files: urls }),
      }
    );

    const data = await res.json();

    console.log("after called purge")

    if (!res.ok || !data.success) {
      console.error('❌ Cloudflare purge error:', {
        status: res.status,
        statusText: res.statusText,
        errors: data.errors,
        messages: data.messages
      });
      return { success: false, error: data.errors || 'Unknown error' };
    } else {
      console.log('✅ Cloudflare cache purged successfully for:', urls.length, 'URLs');
      return { success: true, data };
    }
  } catch (err) {
    console.error('❌ Cloudflare purge fetch error:', err);
    return { success: false, error: err.message };
  }
};
