

export const purgeCloudflareCache = async (paths: string[]) => {
  const zoneId = process.env.CLOUDFLARE_ZONE_ID;
  const token = process.env.CLOUDFLARE_API_TOKEN;
  const domain = process.env.CLOUDFLARE_SITE_URL;

  if (!zoneId || !token || !domain) {
    console.error('❌ Missing Cloudflare ENV vars');
    return;
  }

  const urls = paths.map((path) => `${domain}${path}`);

  console.log("urls:", urls)
  console.log("before calling purge")

  try {
    const res = await fetch(
      `https://api.cloudflare.com/client/v4/zones/${zoneId}/purge_cache`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ files: urls }),
      }
    );

    const data = await res.json();

    console.log("after called purge")

    if (!res.ok || !data.success) {
      console.error('❌ Cloudflare purge error:', data.errors || data);
    } else {
      console.log('✅ Cloudflare cache purged for:', urls);
    }

    return data;
  } catch (err) {
    console.error('❌ Cloudflare purge fetch error:', err);
  }
};
