import { PayloadRequest } from "payload/types";

export const getBlogById = async (req: PayloadRequest, res: any) => {
  const { blogId } = req.params;

  if (!blogId) {
    return res.status(404).json({ error: "Blog not found" });
  }

  try {
    const blog: any = await req.payload.findByID({
      collection: "blogs",
      id: blogId,
      depth: 1,
    });

    if (!blog) {
      return res.status(404).json({ error: "Blog not found" });
    }

    const blogData = {
      id: blog.id,
      title: blog.title,
      content: blog.content,
      richText: blog.richText,
      images: blog.images,
      status: blog.status,
      tags: blog.tags,
      time: blog.time,
      needsApproval: blog.needsApproval,
      user: blog.user?.id,
      slug: blog.slug,
      isFeatured: blog.isFeatured,
      createdAt: blog.createdAt,
    };
    res.setHeader('Cache-Control', 'public, max-age=1800, s-maxage=1800, stale-while-revalidate=3600');
    return res.status(200).json({ success: true, blogData });
  } catch (error) {
    console.error("Error fetching blog:", error);
    return res.status(500).json({ error: "Error fetching blog" });
  }
};
