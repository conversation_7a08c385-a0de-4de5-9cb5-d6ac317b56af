import { PayloadRequest } from "payload/types";

export const getFeaturedBlogs = async (req: PayloadRequest, res: any) => {
  try {
    console.log("inside featured blog")
    const blog: any = await req.payload.find({
      collection: "blogs",
      limit: 4,
      where: {
        isFeatured: {
          equals: true,
        },
      },
      depth: 1,
    });

    if (!blog) {
      return res.status(404).json({ error: "Blog not found" });
    }

    const FeaturedBlogs = blog.docs.map((blog) => ({
      id: blog.id,
      title: blog.title,
      slug: blog.slug,
      content: blog.content,
      images: blog.images,
      tags: blog.tags,
      time: blog.time,
      isFeatured: blog.isFeatured,
      createdAt: blog.createdAt,
    }));
     res.setHeader('Cache-Control', 'private, max-age=0, s-maxage-1800, stale-while-revalidate=60');
    return res.status(200).json({ success: true, data: FeaturedBlogs });
  } catch (error) {
    console.error("Error fetching blog:", error);
    return res.status(500).json({ error: "Error fetching blog" });
  }
};
