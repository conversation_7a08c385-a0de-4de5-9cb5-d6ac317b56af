import { PayloadRequest } from "payload/types";


export const getAllBlogs = async (req: PayloadRequest, res: any) => {
  const   { page ,limit } :any  =req.query;

  try {
    console.log("inside get blogs")
    const blog: any = await req.payload.find({
      collection: "blogs",
      where: {
        status: { 
          equals: "approved" 
        },
      },
      limit, 
      depth: 1,
      page ,

    
    });

    if (!blog) {
      return res.status(404).json({ error: "Blog nooot found" });
    }

    const AllBlogs = blog.docs.map((blog) => ({
      id: blog.id,
      title: blog.title,
      slug: blog.slug,
      content: blog.content,
      images: blog.images,
      tags: blog.tags,
      time: blog.time,
      isFeatured: blog.isFeatured,
      createdAt: blog.createdAt,
    }));

    console.log("before set header")
    res.setHeader('Cache-Control', 'private, max-age=0, s-maxage-1800, stale-while-revalidate=60');
    console.log("after set header")

    return res
      .status(200)
      .json({ success: true, data: AllBlogs });
  } catch (error) {
    console.error("Error fetching blog:", error);
    return res.status(500).json({ error: "Error fetching blog" });
  }
};
